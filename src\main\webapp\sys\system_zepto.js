﻿window.isdebug = 0;
window.lan = 'zh';
window.bucketName = 'tbptfile';
window.islocal = true;//是否上传到服务器
if(!window.islocal){
    window.ossPrefix = '//' + bucketName + '.oss-cn-beijing.aliyuncs.com/';
} else {
    window.ossPrefix = location.origin + "/fuyou/";
    if(location.origin.indexOf("localhost") < 0){
        window.ossPrefix = location.origin + "/";
    }
}
window.uploadPrefix = 'userfiles/chengdu/fuyou';
window.ossParam = '?x-oss-process=image/resize,h_150';
window.ossParam2 = '?x-oss-process=image/resize,m_fill,h_150,w_150';
var v = Arg('v');
var ov = localStorage.getItem("v");
if(ov && v && v==ov){
	
} else {
	cssAddv(v||new Date().getTime());
}
function cssAddv(v){
	localStorage.setItem("v",v);
	$('head link').each(function(){
		var newhref = $(this).attr('href') + '?v=' + v;
		$(this).attr('href',newhref )
	});
}

$.projectpath = "/fuyoumini";
$.smurl = $.fn.smurl =  window.location.protocol + "//" + location.host + $.projectpath + "/Enter";
//cb 回调 arr 参数 t 连接类型 did 数据库标识 pobj 扩展参数 timeout 超时时间（待废除）
$.sm = $.fn.sm = function(cb, arr, t, did, pobj, timeout) {
	var strp = "";
	var ismul = 0;
	if (typeof arr[0] == "object") {
		ismul = 1;
		var arr2 = [];
		for (var i = 0; i < arr.length; i++) {
			arr2.push(arr[i].join('%15'));
		}
		strp = arr2.join('%18');
	} else {
		strp = arr.join('%15');
	}
	if(t&&typeof t=="object"){
		pobj = t;
	}
	var mid = '';
	if(pobj&&pobj.mid){
		mid = pobj.mid;
	} else if(window.objdata&&window.objdata.mid){
		mid = window.objdata.mid;
	} else if(window.Arg&&Arg('mid')){
		mid = Arg('mid');
	}
	var ajaxobj = {
		type : "post",
		url : $.smurl,
		timeout : timeout || 100000,
		contentType: "application/x-www-form-urlencoded; charset=utf-8", 
		data : {
			"ismul" : ismul,
			"arr" : strp,
			"t" : t || "",
			"did" : did || "",
			"lan" : window.lan
		},
		success : function(str) {
			if (str) {
				try {
					var obj = JSON.parse(str);
					$.decodeObject(obj);
				} catch (e) {
					return cb(null, "系统错误，请联系管理员");
				}
				if ($.isArray(obj)) {
					cb(obj, null);
				} else if (typeof obj == "object") {
					if (obj.error && obj.error == "nologin") {
						alert("登录已过期，请重新登录！");
						location.href = "http://" + location.host + $.projectpath + "/login.html";
					} else if (window.isdebug && obj.error) {
						// 重新发送请求
						errhandler("http://" + location.host + $.projectpath + "/Enter", strp, pobj, arr, obj);
					} else {
						//obj.mess = decodeURIComponent(obj.mess);
						if (obj.error) {
							try {
								obj.error = JSON.parse(obj.error);
							} catch (e) {
								// TODO: handle exception
							}
							console.log(strp + "   " +obj.error);
						}
						cb(obj.re, obj.error, obj);
					}
				} else {
					cb(obj);
				}
			} else {
				cb(null, "结果为空");
			}
		},
		error : function(XMLHttpRequest, textStatus, errorThrown) {
			if(textStatus === "abort"){
				//页面放弃了ajax请求，由于快速回退，iframe关闭导致请求终止
				return;
			}
			cb(null, "系统错误，请稍后再试");
		},
		complete : function(XMLHttpRequest,status){ //请求完成后最终执行参数
			if(status == 'timeout'){
				//alert('请求超时，请稍后再试！');
			}
		},
		async : true
	};
	if(mid){
		ajaxobj.data.mid = mid;
	}
	if(pobj)
		ajaxobj = $.extend(ajaxobj,pobj);
	$.ajax(ajaxobj);
};
$.SpecialChars={"%":"%25",",":"%2C","'":"%27",'"':"%22","\\+":"%2B","\\r":"%0D","\\n":"%0A","\\\\r":"\\\\r","\\\\n":"\\\\n","\\t":"%09",'\"':'\\%22'};
$.DecodeSpChar=function(str)
{
    var a=$.SpecialChars;
    if(!$.DeSpecialChars)
    {
        $.DeSpecialChars={};
        for(var i in a)
        {
            if(typeof(a[i])=="string"){
                var v1=a[i];
                $.DeSpecialChars[v1]=i;
            }
        }
    }
    var b=$.DeSpecialChars;
    var t="";
    for(var i in b)
    {
        if(typeof(b[i])=="string")t+=i+"|";
    }
    t=t.substr(0,t.length-1);
    var p=new RegExp(t,"gim");
    return str.replace(p,function(c){
        if(c=="\\r") return "\\r";
        if(c=="\\n") return "\\n";
        if(c=="%2B") return "+";
        return $.DeSpecialChars[c]||"";
    });
};
$.decodeObject=function(obj)
{
    if(typeof(obj)!="object")return obj;
    for(var key in obj)
    {
        var kv=obj[key];
        try{
            if(typeof(kv)=="string")
                kv=decodeURIComponent(kv);
        }catch(e){        
        }
        if(typeof(obj[key])=="string")obj[key]=$.DecodeSpChar(kv);
        if(typeof(obj[key])=="object")$.decodeObject(obj[key]);
    }
};
/*
     功能：将字符串\r\n处理成换行符
     文本区域textarea和编辑器 换行符\r\n的显示为编码后的特殊字符，即显示换行
     编辑器赋值时 使用jQuery.toCharCode(v)的返回值
     文本区域textarea 使用jQuery.toCharCode(v)的返回值或者直接使用$('#id').areaval(v);
     */
$.toCharCode = function (str) {//debugger;
    var spc = {
        "\\\\\\\\r": "\\r",
        "\\\\\\\\n": "\\n",
        "\\\\\\\\t": "\\t",
        "\\\\\\\\": "\\",
        "\\\\r": String.fromCharCode(13),
        "\\\\n": String.fromCharCode(10),
        "\\\\t": String.fromCharCode(9)
    };
    var t = "";
    for (var i in spc) {
        if (typeof(spc[i]) == "string") t += i + "|";
    }
    t = t.substr(0, t.length - 1);
    var p = new RegExp(t, "gim");
//	    var r=str.match(p);
    return str.replace(p, function (c) {
        return spc["\\\\" + c] || spc["\\" + c] || spc[c];
    });
};
$.EncodeSpChar=function(str,h){//debugger;'&acute;"&quot;明确是\r \n的数据库存的是\\r\\n
    var a=$.SpecialChars;
    var t="";
    for(var i in a)
    {
        if(typeof(a[i])=="string")t+=i+"|";
    }
    t=t.substr(0,t.length-1);
    if(h){
        var o={"'":"&acute;",'"':"&quot;"};
        str=str.replace(/\'|\"/g,function(c){
            return o[c];
        });        
    }
    var p=new RegExp(t,"gim");
    return str.replace(p,function(c){
        //debugger;
        var r1= $.SpecialChars["\\"+c]||$.SpecialChars[c] ;
        if(!r1){
//            if(c=="\n"||c=="\r"||c=="\t")return "";
            if(c=="\n")return "%0A";
            if(c=="\r")return "%0D";
            if(c=="\t")return "%09";
        }else
            return r1;
    });
};
//转义相关
$.nodeName = function (element, nodeName) {
	return element.nodeName.toLowerCase() === nodeName;
};
$.fn.encodeval=function()
{
    return ( this.length ? (($.nodeName(this[0], "input")||$.nodeName(this[0], "textarea"))?$.EncodeSpChar($(this[0]).val()):$(this[0]).val()) : null );
};
$.fn.encodehtml=function()
{
    return ( this.length ? $.EncodeSpChar(this[0].innerHTML) : null );
};
$.fn.encodetext=function()
{
    return ( this.length ? $.EncodeSpChar($(this).text()) : null );
};
$.fn.encodehtmlval=function()
{
    return ( this.length ? $.EncodeSpChar(this[0].value,true) : null );
};
(function () {
    'use strict';

    var rx_one = /^[\],:{}\s]*$/, rx_two = /\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,
        rx_three = /"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, rx_four = /(?:^|:|,)(?:\s*\[)+/g,
        rx_escapable = /[\\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
        rx_dangerous = /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;

    function f(n) {
        // Format integers to have at least two digits.
        return n < 10 ? '0' + n : n;
    }

    function this_value() {
        return this.valueOf();
    }

    if (typeof Date.prototype.toJSON !== 'function') {

        Date.prototype.toJSON = function () {

            return isFinite(this.valueOf()) ? this.getUTCFullYear() + '-' + f(this.getUTCMonth() + 1) + '-' + f(this.getUTCDate()) + 'T' + f(this.getUTCHours()) + ':' + f(this.getUTCMinutes()) + ':' + f(this.getUTCSeconds()) + 'Z' : null;
        };

        Boolean.prototype.toJSON = this_value;
        Number.prototype.toJSON = this_value;
        String.prototype.toJSON = this_value;
    }

    var gap, indent, meta, rep;

    function quote(string) {

        // If the string contains no control characters, no quote characters,
        // and no
        // backslash characters, then we can safely slap some quotes around it.
        // Otherwise we must also replace the offending characters with safe
        // escape
        // sequences.

        rx_escapable.lastIndex = 0;
        return rx_escapable.test(string) ? '"' + string.replace(rx_escapable, function (a) {
            var c = meta[a];
            return typeof c === 'string' ? c : '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
        }) + '"' : '"' + string + '"';
    }

    function str(key, holder) {

        // Produce a string from holder[key].

        var i, // The loop counter.
            k, // The member key.
            v, // The member value.
            length, mind = gap, partial, value = holder[key];

        // If the value has a toJSON method, call it to obtain a replacement
        // value.

        if (value && typeof value === 'object' && typeof value.toJSON === 'function') {
            value = value.toJSON(key);
        }

        // If we were called with a replacer function, then call the replacer to
        // obtain a replacement value.

        if (typeof rep === 'function') {
            value = rep.call(holder, key, value);
        }

        // What happens next depends on the value's type.

        switch (typeof value) {
            case 'string':
                return quote(value);

            case 'number':

                // JSON numbers must be finite. Encode non-finite numbers as null.

                return isFinite(value) ? String(value) : 'null';

            case 'boolean':
            case 'null':

                // If the value is a boolean or null, convert it to a string. Note:
                // typeof null does not produce 'null'. The case is included here in
                // the remote chance that this gets fixed someday.

                return String(value);

            // If the type is 'object', we might be dealing with an object or an
            // array or
            // null.

            case 'object':

                // Due to a specification blunder in ECMAScript, typeof null is
                // 'object',
                // so watch out for that case.

                if (!value) {
                    return 'null';
                }

                // Make an array to hold the partial results of stringifying this
                // object value.

                gap += indent;
                partial = [];

                // Is the value an array?

                if (Object.prototype.toString.apply(value) === '[object Array]') {

                    // The value is an array. Stringify every element. Use null as a
                    // placeholder
                    // for non-JSON values.

                    length = value.length;
                    for (i = 0; i < length; i += 1) {
                        partial[i] = str(i, value) || 'null';
                    }

                    // Join all of the elements together, separated with commas, and
                    // wrap them in
                    // brackets.

                    v = partial.length === 0 ? '[]' : gap ? '[\n' + gap + partial.join(',\n' + gap) + '\n' + mind + ']' : '[' + partial.join(',') + ']';
                    gap = mind;
                    return v;
                }

                // If the replacer is an array, use it to select the members to be
                // stringified.

                if (rep && typeof rep === 'object') {
                    length = rep.length;
                    for (i = 0; i < length; i += 1) {
                        if (typeof rep[i] === 'string') {
                            k = rep[i];
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                } else {

                    // Otherwise, iterate through all of the keys in the object.

                    for (k in value) {
                        if (Object.prototype.hasOwnProperty.call(value, k)) {
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                }

                // Join all of the member texts together, separated with commas,
                // and wrap them in braces.

                v = partial.length === 0 ? '{}' : gap ? '{\n' + gap + partial.join(',\n' + gap) + '\n' + mind + '}' : '{' + partial.join(',') + '}';
                gap = mind;
                return v;
        }
    }

    // If the JSON object does not yet have a stringify method, give it one.

    if (typeof JSON.stringify !== 'function' || true) {
        meta = { // table of character substitutions
            '\b': '\\b',
            '\t': '\\t',
            '\n': '\\n',
            '\f': '\\f',
            '\r': '\\r',
            '"': '\\"',
            '\\': '\\\\'
        };
        JSON.stringify = function (value, replacer, space) {

            // The stringify method takes a value and an optional replacer, and
            // an optional
            // space parameter, and returns a JSON text. The replacer can be a
            // function
            // that can replace values, or an array of strings that will select
            // the keys.
            // A default replacer method can be provided. Use of the space
            // parameter can
            // produce text that is more easily readable.

            var i;
            gap = '';
            indent = '';

            // If the space parameter is a number, make an indent string
            // containing that
            // many spaces.

            if (typeof space === 'number') {
                for (i = 0; i < space; i += 1) {
                    indent += ' ';
                }

                // If the space parameter is a string, it will be used as the
                // indent string.

            } else if (typeof space === 'string') {
                indent = space;
            }

            // If there is a replacer, it must be a function or an array.
            // Otherwise, throw an error.

            rep = replacer;
            if (replacer && typeof replacer !== 'function' && (typeof replacer !== 'object' || typeof replacer.length !== 'number')) {
                throw new Error('JSON.stringify');
            }

            // Make a fake root object containing our value under the key of ''.
            // Return the result of stringifying the value.

            return str('', {
                '': value
            });
        };
    }

    // If the JSON object does not yet have a parse method, give it one.

    if (typeof JSON.parse !== 'function' || true) {
        JSON.parse = function (text, reviver) {

            // The parse method takes a text and an optional reviver function,
            // and returns
            // a JavaScript value if the text is a valid JSON text.

            var j;

            function walk(holder, key) {

                // The walk method is used to recursively walk the resulting
                // structure so
                // that modifications can be made.

                var k, v, value = holder[key];
                if (value && typeof value === 'object') {
                    for (k in value) {
                        if (Object.prototype.hasOwnProperty.call(value, k)) {
                            v = walk(value, k);
                            if (v !== undefined) {
                                value[k] = v;
                            } else {
                                delete value[k];
                            }
                        }
                    }
                }
                return reviver.call(holder, key, value);
            }

            // Parsing happens in four stages. In the first stage, we replace
            // certain
            // Unicode characters with escape sequences. JavaScript handles many
            // characters
            // incorrectly, either silently deleting them, or treating them as
            // line endings.

            text = String(text);
            rx_dangerous.lastIndex = 0;
            if (rx_dangerous.test(text)) {
                text = text.replace(rx_dangerous, function (a) {
                    return '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
                });
            }

            // In the second stage, we run the text against regular expressions
            // that look
            // for non-JSON patterns. We are especially concerned with '()' and
            // 'new'
            // because they can cause invocation, and '=' because it can cause
            // mutation.
            // But just to be safe, we want to reject all unexpected forms.

            // We split the second stage into 4 regexp operations in order to
            // work around
            // crippling inefficiencies in IE's and Safari's regexp engines.
            // First we
            // replace the JSON backslash pairs with '@' (a non-JSON character).
            // Second, we
            // replace all simple value tokens with ']' characters. Third, we
            // delete all
            // open brackets that follow a colon or comma or that begin the
            // text. Finally,
            // we look to see that the remaining characters are only whitespace
            // or ']' or
            // ',' or ':' or '{' or '}'. If that is so, then the text is safe
            // for eval.

            if (rx_one.test(text.replace(rx_two, '@').replace(rx_three, ']').replace(rx_four, ''))) {

                // In the third stage we use the eval function to compile the
                // text into a
                // JavaScript structure. The '{' operator is subject to a
                // syntactic ambiguity
                // in JavaScript: it can begin a block or an object literal. We
                // wrap the text
                // in parens to eliminate the ambiguity.

                j = eval('(' + text + ')');

                // In the optional fourth stage, we recursively walk the new
                // structure, passing
                // each name/value pair to a reviver function for possible
                // transformation.

                return typeof reviver === 'function' ? walk({
                    '': j
                }, '') : j;
            }

            // If the text is not JSON parseable, then a SyntaxError is thrown.

            throw new SyntaxError('JSON.parse');
        };
    }
}());